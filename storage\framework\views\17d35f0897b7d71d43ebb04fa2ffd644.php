<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
        <title><?php echo e(config('app.name')); ?> - Blog</title>
        <link><?php echo e(route('blog')); ?></link>
        <description>Latest blog posts from <?php echo e(config('app.name')); ?></description>
        <language>en-us</language>
        <lastBuildDate><?php echo e(now()->toRssString()); ?></lastBuildDate>
        <atom:link href="<?php echo e(route('blog.rss')); ?>" rel="self" type="application/rss+xml" />
        
        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <item>
            <title><![CDATA[<?php echo e($post->title); ?>]]></title>
            <link><?php echo e(route('post', $post->slug)); ?></link>
            <description><![CDATA[<?php echo e($post->excerpt); ?>]]></description>
            <pubDate><?php echo e($post->created_at->toRssString()); ?></pubDate>
            <guid isPermaLink="true"><?php echo e(route('post', $post->slug)); ?></guid>
        </item>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </channel>
</rss>
<?php /**PATH E:\websites\csn\resources\views/blog/rss.blade.php ENDPATH**/ ?>