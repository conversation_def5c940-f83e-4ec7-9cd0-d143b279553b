<?php $__env->startSection('content'); ?>
    <!-- <PERSON> Header Start -->
    <div class="page-header bg-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <!-- Page Header Box Start -->
                    <div class="page-header-box">
                        <h1 class="text-anime-style-2" data-cursor="-opaque">
                            <div style="position:relative;display:inline-block;">
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    I</div>
                                <div
                                    style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                    T</div>
                            </div> <span>
                                <div style="position:relative;display:inline-block;margin-left: 15px;">
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        R</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        e</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        c</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        r</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        u</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        i</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        t</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        i</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        n</div>
                                    <div
                                        style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                        g</div>

                                </div>
                            </span>
                        </h1>

                    </div>
                    <!-- Page Header Box End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Page Service Single Start -->
    <div class="page-service-single">
        <div class="container">
            <div class="row">
                <?php if (isset($component)) { $__componentOriginal4bfab3b54dc0ada121331c8e3d0362da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4bfab3b54dc0ada121331c8e3d0362da = $attributes; } ?>
<?php $component = App\View\Components\ServiceSidebar::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('service-sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\ServiceSidebar::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4bfab3b54dc0ada121331c8e3d0362da)): ?>
<?php $attributes = $__attributesOriginal4bfab3b54dc0ada121331c8e3d0362da; ?>
<?php unset($__attributesOriginal4bfab3b54dc0ada121331c8e3d0362da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4bfab3b54dc0ada121331c8e3d0362da)): ?>
<?php $component = $__componentOriginal4bfab3b54dc0ada121331c8e3d0362da; ?>
<?php unset($__componentOriginal4bfab3b54dc0ada121331c8e3d0362da); ?>
<?php endif; ?>
                <div class="col-lg-8">
                    <!-- Service Single Content Start -->
                    <div class="service-single-content">
                        <!-- Service Feature Image Start -->
                        <div class="service-feature-image">
                            <figure class="image-anime reveal"
                                style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                <img src="<?php echo e(asset('assets/images/service-4.jpg')); ?>" alt=""
                                    style="transform: translate(0px, 0px);">
                            </figure>
                        </div>
                        <!-- Service Feature Image End -->

                        <!-- Service Entry Start -->
                        <div class="service-entry">
                            <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">We specialise in
                                finding the right talent for your IT needs, whether you're looking to fill a specific role
                                or build a dedicated team. Our comprehensive recruitment approach combines deep technical
                                expertise with industry insights to connect you with exceptional IT professionals who drive
                                innovation and growth.</p>

                            <p class="wow fadeInUp" data-wow-delay="0.2s"
                                style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">Our expertise
                                spans software development, cybersecurity, cloud architecture, data science, DevOps, and
                                emerging technologies. We understand the unique challenges of IT recruitment and leverage
                                our extensive network, rigorous screening processes, and cultural fit assessments to deliver
                                candidates who not only meet your technical requirements but also align with your
                                organizational values and goals.</p>

                            <!-- Service Benefits Start -->
                            <div class="service-benefits">
                                <h2 class="text-anime-style-2">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            B</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            f</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            i</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            t</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            s</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;margin-left: 10px;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                I</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                T</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;margin-left: 10px;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                c</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                u</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                i</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                t</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                i</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                n</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                g</div>
                                        </div>
                                    </span>
                                </h2>

                                <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">IT recruiting
                                    services accelerate your hiring process, reduce recruitment costs, and ensure you secure
                                    top-tier technical talent that drives innovation and competitive advantage in today's
                                    technology-driven marketplace.</p>

                                <!-- Service Benefit List Start -->
                                <div class="service-benefit-list wow fadeInUp" data-wow-delay="0.2s"
                                    style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                    <ul>
                                        <li>access to top-tier IT professionals</li>
                                        <li>reduced time-to-hire &amp; recruitment costs</li>
                                        <li>technical skills assessment &amp; validation</li>
                                        <li>cultural fit &amp; team compatibility evaluation</li>
                                        <li>flexible hiring models &amp; contract options</li>
                                        <li>ongoing support &amp; candidate retention</li>
                                    </ul>
                                </div>
                                <!-- Service Benefit List End -->

                                <!-- Service Benefits Images Start -->
                                <div class="service-benefits-images">
                                    <!-- Service Benefits Img Start -->
                                    <div class="service-benefits-img">
                                        <figure class="image-anime reveal"
                                            style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                            <img src="<?php echo e(asset('assets/images/service-benefit-img-1.jpg')); ?>" alt=""
                                                style="transform: translate(0px, 0px);">
                                        </figure>
                                    </div>
                                    <!-- Service Benefits Img End -->

                                    <!-- Service Benefits Img Start -->
                                    <div class="service-benefits-img">
                                        <figure class="image-anime reveal"
                                            style="transform: translate(0px, 0px); opacity: 1; visibility: inherit;">
                                            <img src="<?php echo e(asset('assets/images/service-benefit-img-2.jpg')); ?>" alt=""
                                                style="transform: translate(0px, 0px);">
                                        </figure>
                                    </div>
                                    <!-- Service Benefits Img End -->
                                </div>
                                <!-- Service Benefits Images End -->
                            </div>
                            <!-- Service Benefits End -->

                            <!-- Service Design Process Start -->
                            <div class="service-design-process">
                                <h2 class="text-anime-style-2">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            R</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            c</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            r</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            u</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            i</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            t</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            m</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            e</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            t</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;margin-left: 10px;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                p</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                o</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                c</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                        </div>
                                    </span>
                                </h2>

                                <p class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">Our
                                    recruitment process combines strategic sourcing, comprehensive screening, and cultural
                                    assessment to identify and secure exceptional IT talent that aligns with your technical
                                    requirements and organizational culture.</p>

                                <!-- Design Process Item List Start -->
                                <div class="design-process-item-list">
                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.2s"
                                        style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="<?php echo e(asset('assets/images/icon-design-process-1.svg')); ?>"
                                                alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>sourcing</h3>
                                            <p>Strategic talent sourcing &amp; candidate identification.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->

                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.4s"
                                        style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="<?php echo e(asset('assets/images/icon-design-process-2.svg')); ?>"
                                                alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>screening</h3>
                                            <p>Technical assessment &amp; cultural fit evaluation.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->

                                    <!-- Design Process Item Start -->
                                    <div class="design-process-item wow fadeInUp" data-wow-delay="0.6s"
                                        style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                                        <div class="icon-box">
                                            <img src="<?php echo e(asset('assets/images/icon-design-process-3.svg')); ?>"
                                                alt="">
                                        </div>

                                        <div class="design-process-item-content">
                                            <h3>placement</h3>
                                            <p>Seamless onboarding &amp; ongoing support.</p>
                                        </div>
                                    </div>
                                    <!-- Design Process Item End -->
                                </div>
                                <!-- Design Process Item List End -->
                            </div>
                            <!-- Service Design Process End -->
                        </div>
                        <!-- Service Entry End -->

                        <!-- Page Single Faqs Start -->
                        <div class="page-single-faqs">
                            <!-- Section Title Start -->
                            <div class="section-title">
                                <h3 class="wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">FAQ's</h3>
                                <h2 class="text-anime-style-2" data-cursor="-opaque">
                                    <div style="position:relative;display:inline-block;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            F</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            i</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            n</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            d</div>
                                    </div>
                                    <div style="position:relative;display:inline-block;margin-left: 10px;">
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            y</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            o</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            u</div>
                                        <div
                                            style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                            r</div>
                                    </div> <span>
                                        <div style="position:relative;display:inline-block;margin-left: 10px;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                a</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                n</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                w</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                s</div>
                                        </div>
                                        <div style="position:relative;display:inline-block;margin-left: 10px;">
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                h</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                r</div>
                                            <div
                                                style="position: relative; display: inline-block; opacity: 1; visibility: inherit; transform: translate(0px, 0px);">
                                                e</div>
                                        </div>
                                    </span>
                                </h2>
                            </div>
                            <!-- Section Title End -->

                            <!-- FAQ Accordion Start -->
                            <div class="faq-accordion" id="accordion">
                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp"
                                    style="visibility: visible; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                            What IT roles do you specialize in recruiting for?
                                        </button>
                                    </h2>
                                    <div id="collapse1" class="accordion-collapse collapse show"
                                        aria-labelledby="heading1" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We recruit for all IT roles including software developers, cybersecurity
                                                specialists, cloud architects, data scientists, DevOps engineers, IT
                                                managers, system administrators, and emerging technology experts across all
                                                experience levels.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.2s"
                                    style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading2">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false"
                                            aria-controls="collapse2">
                                            How do you assess technical skills?
                                        </button>
                                    </h2>
                                    <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We use comprehensive technical assessments including coding challenges,
                                                system design interviews, practical problem-solving scenarios, and peer
                                                reviews by our technical experts to validate candidates' skills and
                                                experience.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.4s"
                                    style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading3">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false"
                                            aria-controls="collapse3">
                                            What hiring models do you offer?
                                        </button>
                                    </h2>
                                    <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>We offer flexible hiring models including permanent placements, contract
                                                positions, contract-to-hire arrangements, and dedicated team building. Our
                                                approach adapts to your specific needs and timeline requirements.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.6s"
                                    style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading4">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false"
                                            aria-controls="collapse4">
                                            How long does the recruitment process take?
                                        </button>
                                    </h2>
                                    <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>Our typical recruitment timeline is 2-4 weeks for most positions, depending
                                                on role complexity and requirements. We provide regular updates throughout
                                                the process and can expedite urgent placements when needed.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.8s"
                                    style="visibility: visible; animation-delay: 0.8s; animation-name: fadeInUp;">
                                    <h2 class="accordion-header" id="heading5">
                                        <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="true"
                                            aria-controls="collapse5">
                                            Do you provide post-placement support?
                                        </button>
                                    </h2>
                                    <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5"
                                        data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p>Yes, we provide comprehensive post-placement support including onboarding
                                                assistance, performance check-ins, and replacement guarantees. Our goal is
                                                to ensure successful long-term placements and candidate satisfaction.</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->
                            </div>
                            <!-- FAQ Accordion End -->
                        </div>
                        <!-- Page Single Faqs End -->
                    </div>
                    <!-- Service Single Content End -->
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.App', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\websites\csn\resources\views/services/it-recruiting.blade.php ENDPATH**/ ?>